import {
    AppointmentConsultationType,
    ConsultationType,
    getConsultationTypesInfo
} from "@/types/consultationTypes/ConsultationType";
import {convertTimeStringToMinutes} from "@/utils/dateUtils";
import {getSpanishNameFromAppointmentState} from "@/utils/AppointmentStateUtils";
import {AppointmentSource, AppointmentState} from "@/types/professional-schedules";
import {ConsultationTypeInfo} from "@/components/schedulecomponents/ConsultationInfoTable";
import {DoctorsForMedicalCenter} from "@/types/doctors/doctorsForMedicalCenter";

export class ProfessionalAppointment {

    constructor(
        public id: number,
        public phone: string,
        public email: string,
        public state: AppointmentState,
        public date: string,
        public details: string,
        public source: AppointmentSource,
        public patientId: number,
        public patientName: string,
        public identificationNumber: string,
        public healthInsuranceId: number | null,
        public healthInsuranceInformation: AppointmentHealthInsurance,
        public price: number,
        public startTime: string,
        public appointmentIntervalAmount: number,
        public consultationTypes: AppointmentConsultationType[],
        public totalPatientAppointments: number,
        public attendancePercentage: number
    ) {
    }

    static fromJSON(json: Record<string, unknown>): ProfessionalAppointment {
        const consultationTypes: AppointmentConsultationType[] = Array.isArray(json.consultationTypes)
            ? (json.consultationTypes as Record<string, unknown>[]).map((ct) => AppointmentConsultationType.fromJSON(ct))
            : [];
        const healthInsuranceInformation = AppointmentHealthInsurance.fromJSON(json.healthInsuranceInformation as Record<string, unknown>);
        return new ProfessionalAppointment(
            json.id as number,
            json.phone as string,
            json.email as string,
            json.state as AppointmentState,
            json.date as string,
            json.details as string,
            json.source as AppointmentSource,
            json.patientId as number,
            json.patientName as string,
            json.identificationNumber as string,
            json.healthInsuranceId as number | null,
            healthInsuranceInformation,
            json.price as number,
            json.startTime as string,
            json.appointmentIntervalAmount as number,
            consultationTypes,
            json.totalPatientAppointments as number,
            json.attendancePercentage as number
        );
    }

    collidesWithTime(time: string, appointmentDuration: number): boolean {
        const appointmentStartMinutes = convertTimeStringToMinutes(this.startTime);
        const slotStartMinutes = convertTimeStringToMinutes(time);
        const appointmentEndMinutes = appointmentStartMinutes + (this.appointmentIntervalAmount || appointmentDuration);


        return slotStartMinutes >= appointmentStartMinutes &&
            slotStartMinutes < appointmentEndMinutes;

    }

    getStateAsSpanishString(): "Cancelado" | "Agendado" | "No se presentó" | "Recepcionado" | "En Atención" | "Atendido" | "Estado desconocido" {
        return getSpanishNameFromAppointmentState(this.state);
    }

    hasHealthInsurance(): boolean {
        return this.healthInsuranceId !== null && this.healthInsuranceId !== undefined;
    }


    getConsultationTypeInfo(consultations: ConsultationType[], doctor: DoctorsForMedicalCenter): ConsultationTypeInfo[] {
        return getConsultationTypesInfo(this.consultationTypes, this.healthInsuranceId, consultations, doctor);
    }

    hasConsultationTypeInfo(consultations: ConsultationType[]): boolean {
        const matchingConsultations = consultations.filter(consultation => this.consultationTypes.some(ct => ct.consultationTypeId === consultation.consultationTypeId));
        matchingConsultations.some(consultation => {
            consultation.hasConsultationTypeInfo(this.healthInsuranceId)
        })
        return false;
    }


    hasInstructionInfo(consultations: ConsultationType[]): boolean {
        return consultations.some(consultation =>
            this.consultationTypes.some(ct =>
                ct.consultationTypeId === consultation.consultationTypeId && consultation.hasInstructions()
            )
        );
    }

    getInstructionInfoForConsultation(consultations: ConsultationType[], appointmentConsultation: AppointmentConsultationType): string | undefined {
        const matchingConsultations = consultations.find(consultation => consultation.consultationTypeId === appointmentConsultation.consultationTypeId);
        return matchingConsultations?.instructions;
    }

    getConsultationTypesAsString(): string {
        return this.consultationTypes.map(ct => ct.name).join(", ");
    }

    hasPrice(): boolean {
        return this.price !== null && this.price !== undefined;
    }

    isInRange(start: string, end: string, appointmentDuration: number) {
        const appointmentStartMinutes = convertTimeStringToMinutes(this.startTime);
        const appointmentEndMinutes = appointmentStartMinutes + (this.appointmentIntervalAmount * appointmentDuration);
        const rangeStartMinutes = convertTimeStringToMinutes(start);
        const rangeEndMinutes = convertTimeStringToMinutes(end);
        return appointmentStartMinutes >= rangeStartMinutes && appointmentEndMinutes <= rangeEndMinutes;
    }

}


export class AppointmentHealthInsurance {
    constructor(
        public name: string,
        public plan: string
    ) {
    }

    static fromJSON(json: Record<string, unknown>): AppointmentHealthInsurance {
        return new AppointmentHealthInsurance(
            json.name as string,
            json.plan as string
        );
    }

    toShowableString() {
        return this.name + " " + this.plan;
    }
}