"use client"

import Re<PERSON>, {use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState} from "react"
import {<PERSON>ertCircle, AlertTriangle, ChevronDown, Info, Search, X} from "lucide-react"
import {But<PERSON>} from "@/components/ui/button"
import {Input} from "@/components/ui/input"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select"
import {Label} from "@/components/ui/label"
import {Switch} from "@/components/ui/switch"
import {Collapsible, CollapsibleContent, CollapsibleTrigger} from "@/components/ui/collapsible"
import {cn} from "@/lib/utils"
import {DEFAULT_COVERAGES} from "@/data/coverages"
import {PhoneInput} from "react-international-phone";
import "react-international-phone/style.css";
import "@/styles/phone-input.css";
import {getSpanishCountries} from "@/data/phoneCountries";
import {PhoneNumberUtil} from 'google-libphonenumber';
import {PatientDetailsDialog} from "@/components/schedulecomponents/patient-details-dialog";
import {ConsultationInfoTable, type ConsultationTypeInfo} from "@/components/schedulecomponents/ConsultationInfoTable";
import {
    AppointmentCreationRequestHealthInsuranceInformation
} from "@/app/api/requestBodies/AppointmentCreationRequestHealthInsuranceInformation";
import {DoctorsForMedicalCenter} from "@/types/doctors/doctorsForMedicalCenter";
import {PatientResponse} from "@/types/patient/patientResponse";
import {
    ConsultationType,
    getConsultationTypesInfoFromHealthInsuranceInformation
} from "@/types/consultationTypes/ConsultationType";
import {BlockedSlot, groupBlockedSlotsByDate, ProfessionalSchedulesResponse} from "@/types/professional-schedules";
import {isSlotBlocked} from "@/utils/appointmentUtils";
import {
    PatientCreationRequestFromMedicalCenter,
    PatientCreationRequestHealthInsuranceInformation
} from "@/app/api/requestBodies/PatientCreationRequestFromMedicalCenter";
import {createPatientFromMedicalCenter} from "@/app/api/utils/appointment/PatientUtils";
import {useAuth} from "@/contexts/AuthContext";

interface CollapsibleSectionProps {
    title: string
    children: React.ReactNode
    isOpen: boolean
    onOpenChange: (open: boolean) => void
    summary?: string | React.ReactNode
    isAnySectionOpen?: boolean
    hasWarning?: boolean
    isComplete?: boolean
}


export interface AppointmentData {
    patientId?: number
    time: string
    healthInsuranceInformation?: AppointmentCreationRequestHealthInsuranceInformation,
    consultationTypeIds: number[]
}

function CollapsibleSection({
                                title,
                                children,
                                isOpen,
                                onOpenChange,
                                summary,
                                isAnySectionOpen,
                                hasWarning,
                                isComplete
                            }: CollapsibleSectionProps) {
    const isGrayedOut = !isOpen && isAnySectionOpen;

    return (
        <Collapsible
            open={isOpen}
            onOpenChange={onOpenChange}
            className={cn(
                "w-full",
                isOpen && "border rounded-lg",
                isOpen && hasWarning && "border-yellow-400 bg-yellow-50",
                isOpen && !hasWarning && "border-gray-200"
            )}
        >
            <CollapsibleTrigger className={cn(
                "flex items-center justify-between w-full p-3",
                "transition-colors duration-200",
                !isOpen && "border rounded-lg",
                !isOpen && hasWarning && "border-yellow-400 bg-yellow-50 text-yellow-800 hover:bg-yellow-100",
                !isOpen && !hasWarning && isComplete && "border-green-400 bg-green-50 text-green-800 hover:bg-green-100",
                isGrayedOut && !hasWarning && !isComplete
                    ? "bg-gray-50 border-gray-100 text-gray-400 hover:bg-gray-100"
                    : !isOpen && !hasWarning && !isComplete ? "border-gray-200 hover:bg-gray-50" : "",
                isOpen && "rounded-b-none",
                isOpen && hasWarning && "hover:bg-yellow-100",
                isOpen && !hasWarning && "hover:bg-gray-50"
            )}>
                <div className="flex flex-col items-start text-left">
                    <div className="flex items-center gap-2">
                        {hasWarning && <AlertTriangle className="h-4 w-4 text-yellow-600 flex-shrink-0"/>}
                        <h3 className={cn("text-sm font-medium", isGrayedOut ? "text-gray-500" : "", hasWarning ? "text-yellow-900" : "", !isOpen && !hasWarning && isComplete ? "text-green-900" : "")}>{title}</h3>
                    </div>
                    {!isOpen && summary && (
                        <span
                            className={cn("text-sm break-words max-w-full mt-1 pl-0", isGrayedOut ? "text-gray-400" : "", hasWarning ? "text-yellow-700" : isComplete ? "text-green-700" : "text-gray-500")}>{summary}</span>
                    )}
                </div>
                <ChevronDown className={cn(
                    "h-4 w-4 transition-transform ml-4 flex-shrink-0",
                    isOpen ? "transform rotate-180" : "",
                    isGrayedOut ? "text-gray-400" : "",
                    hasWarning ? "text-yellow-600" : isComplete && !isOpen ? "text-green-600" : ""
                )}/>
            </CollapsibleTrigger>
            <CollapsibleContent className={cn(
                "space-y-4 transition-all pt-4 px-3 pb-3",
                isOpen ? "" : "h-0 overflow-hidden",
                hasWarning && "border-t border-yellow-300"
            )}>
                {children}
            </CollapsibleContent>
        </Collapsible>
    )
}

const normalizeString = (str: string) => {
    return str && str.normalize("NFD").replace(/[\u0300-\u036f]/g, "").toLowerCase().trim() || ""
}

const dropdownItemStyle = "p-[0.375rem] text-[0.875rem] hover:bg-gray-100 transition-colors cursor-pointer"
const dropdownButtonStyle = "w-full text-left px-[0.5rem] py-[0.375rem] text-[0.875rem] hover:bg-gray-100 border-t"

// Using Patient type from types/patient.ts

const capitalizeName = (name: string) => {
    return name
        .split(" ")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
        .join(" ")
}


interface NewAppointmentFormProps {
    onCancel: () => void
    onConfirm: (data: AppointmentData) => void
    availableSlots: string[]
    selectedTime?: string | null
    selectedDate: Date
    timeSlots: string[]
    doctorInfo: DoctorsForMedicalCenter,
    professionalSchedules: ProfessionalSchedulesResponse,
    patients: PatientResponse[]
    appointmentDuration: number;
    medicalCenterId: number;
    employeeUserId: number;
}


interface patientCreationData {
    name: string,
    lastName: string,
    coverage: string,
    plan: string,
    noCoverage: boolean,
    identificationNumber: string,
    phone: string,
    email?: string,
    dateOfBirth: string
}

export function NewAppointmentForm({
                                       onCancel,
                                       onConfirm,
                                       availableSlots,
                                       selectedTime,
                                       selectedDate,
                                       timeSlots,
                                       doctorInfo,
                                       professionalSchedules,
                                       patients,
                                       appointmentDuration,
                                       medicalCenterId,
                                       employeeUserId
                                   }: NewAppointmentFormProps) {


    const {auth0Token} = useAuth()

    const doctorConsultationTypes = React.useMemo(() =>
            doctorInfo.consultationTypes || [],
        [doctorInfo]
    )

    const blockedSlotsByDate: Record<string, BlockedSlot[]> = useMemo(() => {
        return groupBlockedSlotsByDate(professionalSchedules.blockedSlots)
    }, [professionalSchedules]);

    // Add state for collapsible sections
    const [openSections, setOpenSections] = useState({
        schedule: !selectedTime, // Only open schedule if no time is selected
        patient: !!selectedTime, // Open patient section if time is pre-selected
        coverage: false,
        consultationType: false,
    })

    // Function to handle section toggle
    const toggleSection = (section: keyof typeof openSections) => {
        setOpenSections((prev) => {
            const newState = {...prev}
            // Close all sections
            Object.keys(newState).forEach((key) => {
                newState[key as keyof typeof openSections] = false
            })
            // Open the clicked section
            newState[section] = !prev[section]
            return newState
        })
    }

    const [formData, setFormData] = useState<AppointmentData>({
        time: selectedTime || "",
        consultationTypeIds: []
    })
    const [searchTerm, setSearchTerm] = useState("")
    const [dropdownOpen, setDropdownOpen] = useState(false)
    const [isCreatingNewPatient, setIsCreatingNewPatient] = useState(false)
    const [newPatientData, setNewPatientData] = useState<patientCreationData>({
        name: "",
        lastName: "",
        coverage: "",
        plan: "",
        noCoverage: false,
        identificationNumber: "",
        phone: "+54",
        email: "",
        dateOfBirth: ""
    })
    const [phoneError, setPhoneError] = useState<string>("");

    const phoneUtil = PhoneNumberUtil.getInstance();
    const [hasDefaultCoverage, setHasDefaultCoverage] = useState(false)
    const [useDefaultCoverage, setUseDefaultCoverage] = useState(true)
    const [selectedPatientHealthInsurance, setSelectedPatientHealthInsurance] = useState<PatientCreationRequestHealthInsuranceInformation | null | undefined>()
    const [overrideHealthInsurance, setOverrideHealthInsurance] = useState<PatientCreationRequestHealthInsuranceInformation | null>()
    const [overrideNoCoverage, setOverrideNoCoverage] = useState(false)
    const [timeslotError, setTimeslotError] = useState("")
    const [consultationSearch, setConsultationSearch] = useState("")
    const [showConsultationDropdown, setShowConsultationDropdown] = useState(false)
    const [selectedTypes, setSelectedTypes] = useState<ConsultationType[]>([])
    const [consultationTypeConfirmed, setConsultationTypeConfirmed] = useState(false)
    const [showAllConsultationsModal, setShowAllConsultationsModal] = useState(false)
    const [isCoverageOpen, setIsCoverageOpen] = useState(false)
    const [isPlanOpen, setIsPlanOpen] = useState(false)
    const [isCoverageAccepted, setIsCoverageAccepted] = useState<boolean | null>(null);
    const [showPatientDetails, setShowPatientDetails] = useState(false);
    const [selectedPatientForDetails, setSelectedPatientForDetails] = useState<PatientResponse | null>(null);
    const [showInstructionsDialog, setShowInstructionsDialog] = useState(false);

    const patientSearchRef = useRef<HTMLDivElement>(null)
    const consultationRef = useRef<HTMLDivElement>(null)
    const patientInputRef = useRef<HTMLInputElement>(null)
    const consultationInputRef = useRef<HTMLInputElement>(null)

    const plansByCoverage: Record<string, string[]> = React.useMemo(() => {
        return DEFAULT_COVERAGES.reduce((acc, coverage) => {
            acc[coverage.name] = coverage.plans || [];
            return acc;
        }, {} as Record<string, string[]>);
    }, []);


    const [consultationTypesInfo, setConsultationTypesInfo] = useState<ConsultationTypeInfo[]>([]);

    const consultationTypeInfo: (consultationTypes?: ConsultationType[]) => (ConsultationTypeInfo[]) = useCallback((consultationTypes?: ConsultationType[]) => {
        const typesToShow = consultationTypes || selectedTypes;
        if (typesToShow.length === 0) return [];
        const healthInsuranceInformation: AppointmentCreationRequestHealthInsuranceInformation | undefined = formData.healthInsuranceInformation;
        const doctorConsultationTypes: ConsultationType[] = doctorInfo.consultationTypes.filter(ct => typesToShow.some(t => t.consultationTypeId == ct.consultationTypeId));
        return getConsultationTypesInfoFromHealthInsuranceInformation(typesToShow, healthInsuranceInformation, doctorConsultationTypes);


    }, [doctorInfo.consultationTypes, formData.healthInsuranceInformation, selectedTypes])

    const hasConsultationTypeInfo: boolean = useMemo(() => {
        const typesInfo: ConsultationTypeInfo[] = consultationTypeInfo();
        return typesInfo.some(info => info.requiresMedicalOrder || info.instructions.length > 0 || info.isExcluded || info.copayAmount !== null);
    }, [consultationTypeInfo])

    const showConsultationInfo: (consultationTypes?: ConsultationType[]) => void = useCallback((consultationTypes?: ConsultationType[]) => {
        const typesInfo: ConsultationTypeInfo[] = consultationTypeInfo(consultationTypes);

        if (hasConsultationTypeInfo) {

            setConsultationTypesInfo(typesInfo);
            setShowInstructionsDialog(true);
        }
    }, [consultationTypeInfo, hasConsultationTypeInfo]);

    const isPastTimeSlot = useCallback((time: string): boolean => {
        const now = new Date();
        const [hours, minutes] = time.split(':').map(Number);
        const slotDate = new Date(selectedDate);
        slotDate.setHours(hours, minutes, 0, 0);
        return slotDate.getTime() < (now.getTime() - appointmentDuration * 60 * 1000);
    }, [appointmentDuration, selectedDate]);

    useEffect(() => {
        if (selectedTime) {
            if (!isPastTimeSlot(selectedTime)) {
                setFormData((prev) => ({...prev, time: selectedTime}))

                if (formData.patientId) {
                    setOpenSections({
                        schedule: false,
                        patient: false,
                        coverage: false,
                        consultationType: true
                    })
                } else {
                    setOpenSections({
                        schedule: false,
                        patient: true,
                        coverage: false,
                        consultationType: false
                    })
                }
            }
        }
    }, [selectedTime, selectedDate, isPastTimeSlot, formData.patientId])


    const [infoShownForSelection, setInfoShownForSelection] = useState<boolean>(false);

    useEffect(() => {
        if (consultationTypeConfirmed && selectedTypes.length > 0 && formData.patientId) {
            setOpenSections(prev => ({
                ...prev,
                consultationType: false
            }))
        }
    }, [consultationTypeConfirmed, selectedTypes.length, formData.patientId])

    useEffect(() => {
        setInfoShownForSelection(false);
    }, [selectedTypes]);

    const coverageToCheck: PatientCreationRequestHealthInsuranceInformation | null | undefined = useMemo(() => {
        if (!formData.patientId) {
            return null;
        }
        const patientHealthInsurance: PatientCreationRequestHealthInsuranceInformation | null | undefined = patients.find(p => p.id === formData.patientId)?.healthInsurance?.toPatientCreationRequestHealthInsuranceInformation();
        if (useDefaultCoverage && hasDefaultCoverage) {
            return selectedPatientHealthInsurance;
        } else if (overrideNoCoverage) {
            return null;
        } else if (overrideHealthInsurance) {
            return overrideHealthInsurance;
        } else if (patientHealthInsurance) {
            return patientHealthInsurance;
        }
    }, [formData.patientId, hasDefaultCoverage, overrideHealthInsurance, overrideNoCoverage, patients, selectedPatientHealthInsurance, useDefaultCoverage])

    const checkCoverageAcceptance = useCallback(() => {
        if (!formData.patientId) {
            return null;
        }
        return doctorInfo.isHealthInsuranceAccepted(coverageToCheck)
    }, [coverageToCheck, doctorInfo, formData.patientId])


    useEffect(() => {
        setIsCoverageAccepted(checkCoverageAcceptance())
    }, [checkCoverageAcceptance, coverageToCheck, doctorInfo, formData.patientId]);

    useEffect(() => {
        if (!formData.patientId || selectedTypes.length === 0) {
            return;
        }
        const typesInfo: ConsultationTypeInfo[] = consultationTypeInfo();
        const hasExclusion: boolean = typesInfo.some(info => info.requiresMedicalOrder || info.instructions.length > 0 || info.isExcluded || info.copayAmount !== null);
        if (hasExclusion) {
            setIsCoverageAccepted(false);
        } else if (isCoverageAccepted === false) {
            setIsCoverageAccepted(checkCoverageAcceptance())
        }
    }, [checkCoverageAcceptance, consultationTypeInfo, formData.patientId, isCoverageAccepted, selectedTypes.length]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault()
        if (!formData.time) {
            setTimeslotError("Selecciona un horario para confirmar el turno.")
            return
        }
        setTimeslotError("")

        if (!isCoverageValid()) {
            setOpenSections(prev => ({
                ...prev,
                schedule: false,
                patient: false,
                coverage: true,
                consultationType: false
            }))
            return
        }
        onConfirm({
            ...formData,
            consultationTypeIds: selectedTypes.map(t => t.consultationTypeId),
            healthInsuranceInformation: coverageToCheck ? {...coverageToCheck} : undefined
        })
    };

    const filteredPatients: PatientResponse[] = useMemo(() => {
        const normalizedQuery = normalizeString(searchTerm);
        return patients.filter(patient =>
            normalizeString(patient.name).includes(normalizedQuery) ||
            normalizeString(patient.identificationNumber).includes(normalizedQuery) ||
            normalizeString(patient.phone).includes(normalizedQuery) ||
            normalizeString(patient.email).includes(normalizedQuery)
        );
    }, [patients, searchTerm])

    const formatDate = (date: Date) => {
        const options: Intl.DateTimeFormatOptions = {weekday: "long", year: "numeric", month: "long", day: "numeric"}
        return date.toLocaleDateString("es-ES", options)
    }

    const filteredTimeSlots = timeSlots.filter(slot => {
        if (isPastTimeSlot(slot)) return false;
        // Format date string for isSlotBlocked check
        const dateStr = `${selectedDate.getFullYear()}-${String(selectedDate.getMonth() + 1).padStart(2, "0")}-${String(
            selectedDate.getDate()
        ).padStart(2, "0")}`;
        return !isSlotBlocked(blockedSlotsByDate, dateStr, slot, appointmentDuration);
    });

    const [patientError, setPatientError] = useState<string>("");
    const [patientNotFound, setPatientNotFound] = useState<boolean>(false);
    const [searchPerformed, setSearchPerformed] = useState<boolean>(false);

    const handleCreateNewPatient = () => {
        setPatientError("");
        const identificationNumberToCheck = newPatientData.identificationNumber.trim();
        if (identificationNumberToCheck.length < 7 || identificationNumberToCheck.length > 8) {
            setPatientError("El DNI debe tener entre 7 y 8 dígitos");
            return;
        }
        try {
            const phoneNumber = phoneUtil.parseAndKeepRawInput(newPatientData.phone);
            const isValid = phoneUtil.isValidNumber(phoneNumber);

            if (!isValid) {
                setPhoneError("El número de teléfono no es válido");
                return;
            }
        } catch {
            setPhoneError("El número de teléfono no es válido");
            return;
        }
        const healthInsuranceInformation: PatientCreationRequestHealthInsuranceInformation | null = newPatientData.noCoverage ? null : {
            name: newPatientData.coverage.trim(),
            plan: newPatientData.plan.trim()
        }

        const patientToCreate: PatientCreationRequestFromMedicalCenter = {
            name: newPatientData.name.trim(),
            surname: newPatientData.lastName.trim(),
            identificationNumber: identificationNumberToCheck,
            phone: newPatientData.phone.trim(),
            email: newPatientData.email?.trim() || null,
            dateOfBirth: newPatientData.dateOfBirth,
            healthInsuranceInformation: healthInsuranceInformation
        }

        try {
            createPatientFromMedicalCenter(patientToCreate, employeeUserId, medicalCenterId, auth0Token).then(patientId => {
                setFormData((prev) => ({
                    ...prev,
                    patientId: patientId
                }))
            })
            setIsCreatingNewPatient(false)
            setDropdownOpen(false)
            setSearchTerm(newPatientData.name.trim())
            setHasDefaultCoverage(true)
            setSelectedPatientHealthInsurance(healthInsuranceInformation ? {...healthInsuranceInformation} : null)
            setUseDefaultCoverage(true)
            setIsCoverageAccepted(null); // Reset acceptance check on new patient
        } catch (error) {
            if (error instanceof Error) {
                setPatientError(error.message);
            } else {
                setPatientError("Error al crear el paciente. Intente nuevamente.");
            }
        }
    }

    const filteredConsultations = doctorConsultationTypes.filter((type) =>
        normalizeString(type.name.toLowerCase()).includes(normalizeString(consultationSearch.toLowerCase()))
    )


    // Helper function to show information for the selected consultation types
    const showSelectedConsultationInfo = useCallback(() => {
        // Only proceed if there are selected types and info hasn't been shown yet
        if (selectedTypes.length === 0 || infoShownForSelection) return;

        // Show info for all selected types
        showConsultationInfo(selectedTypes);

        // Mark that info has been shown for this selection
        setInfoShownForSelection(true);
    }, [
        selectedTypes,
        showConsultationInfo,
        infoShownForSelection
    ]);

    const handleConsultationSelect = (type: ConsultationType) => {
        const isAlreadySelected = selectedTypes.some(consultationType => consultationType.name === type.name);
        setSelectedTypes((prev) =>
            isAlreadySelected ? prev.filter((t) => t.name !== type.name) : [...prev, type]
        )
        setConsultationTypeConfirmed(false)
    }

    useEffect(() => {
        const handleClickOutside = (e: MouseEvent) => {
            if (patientSearchRef.current && !patientSearchRef.current.contains(e.target as Node)) {
                setDropdownOpen(false)
                // Reset patient not found state when dropdown is closed
                if (!formData.patientId) {
                    setPatientNotFound(false)
                    setSearchPerformed(false)
                }
            }
            if (consultationRef.current && !consultationRef.current.contains(e.target as Node)) {
                setShowConsultationDropdown(false)
                setConsultationSearch("")
                // Set consultation type as confirmed when clicking outside if types are selected
                if (selectedTypes.length > 0) {
                    setConsultationTypeConfirmed(true)
                    // Show information popup after confirming selection by clicking outside
                    showSelectedConsultationInfo()
                }
            }
        }
        document.addEventListener("mousedown", handleClickOutside)
        return () => document.removeEventListener("mousedown", handleClickOutside)
    }, [formData.patientId, selectedTypes, showSelectedConsultationInfo])


    const clearPatientSelection = () => {
        setFormData((prev) => ({
            ...prev,
            patient: null,
        }))
        setSearchTerm("")
        setHasDefaultCoverage(false)
        setSelectedPatientHealthInsurance(null)
        setUseDefaultCoverage(false)
        setOverrideHealthInsurance(null)
        setOverrideNoCoverage(false)
        setIsCoverageAccepted(null);
    }

    const getScheduleSummary = () => {
        if (!formData.time) return "No seleccionado"
        return formData.time
    }

    const getPatientSummary = useCallback(() => {
        if (!formData.patientId) return "No seleccionado"
        const maybePatient = patients.find(p => p.id === formData.patientId)
        return maybePatient ? maybePatient.name : "no name"
    }, [formData.patientId, patients])

    const getCoverageSummary = () => {
        if (!formData.patientId) return "No disponible"

        const summaryText = coverageToCheck ? `${coverageToCheck.name} ${coverageToCheck.plan}` : "Sin Cobertura"
        if (selectedTypes.length === 0) return summaryText;

        const consultationTypeInformation: ConsultationTypeInfo[] = consultationTypeInfo().filter(info => info.isExcluded);

        if (consultationTypeInformation.length === selectedTypes.length) {
            return (
                <span className="flex items-center gap-1">
            <span>{summaryText} (No aceptada)</span>
          </span>
            );
        } else if (consultationTypeInformation.length > 1) {
            return (
                <span className="flex items-center gap-1">
            <span>{summaryText} (Aceptada parcialmente)</span>
          </span>
            );
        } else if (isCoverageAccepted === false) {
            return (
                <span className="flex items-center gap-1">
          <span>{summaryText} (No aceptada)</span>
        </span>
            );
        }

        return summaryText;
    }

    const getConsultationTypeSummary = () => {
        if (selectedTypes.length === 0) return "No seleccionado"
        return selectedTypes.join(", ")
    }

    const isAnySectionOpen = Object.values(openSections).some(isOpen => isOpen);

    const isSinCobertura = () => {
        if (!formData.patientId) return false;
        return !coverageToCheck
    };

    function isConsultationTypeExcluded(consultationType: ConsultationType): boolean {
        return consultationTypeInfo().filter(info => info.name === consultationType.name && info.isExcluded || (!info.acceptsPrivatePay && isSinCobertura())).length !== 0
    }

    function consultationTypeHasInfo(consultationType: ConsultationType): boolean {
        return consultationTypeInfo().filter(info => info.name === consultationType.name && (info.requiresMedicalOrder || info.instructions.length > 0 || info.isExcluded || info.copayAmount !== null)).length !== 0
    }

    const hasUnbookableConsultationType = () => {
        if (!formData.patientId || selectedTypes.length === 0) return false;
        return consultationTypeInfo().filter(info => info.isExcluded || (!info.acceptsPrivatePay && isSinCobertura())).length > 0
    };

    // Function to check if coverage is valid (not "No seleccionado" and plan is selected when needed)
    const isCoverageValid = () => {
        if (!formData.patientId) return false;
        return !coverageToCheck ? true : (coverageToCheck.name.length !== 0 && coverageToCheck.plan.length !== 0);
    };

    return (
        <>

            {isCreatingNewPatient && (
                <div className="space-y-[0.75rem] border rounded-lg p-[1rem] bg-white border-black">
                    <h3 className="font-medium text-[1.125rem]">Crear nuevo paciente</h3>
                    <div className="space-y-[0.5rem]">
                        <Label htmlFor="name">Nombre y Apellido</Label>
                        <Input
                            id="name"
                            placeholder="Nombre"
                            value={newPatientData.name}
                            autoComplete="off"
                            role="combobox"
                            aria-autocomplete="list"
                            onChange={(e) => {
                                // Only allow letters and spaces
                                const value = e.target.value.replace(/[^a-zA-ZáéíóúÁÉÍÓÚñÑüÜ\s]/g, '');
                                setNewPatientData((prev) => ({...prev, name: capitalizeName(value)}));
                            }}
                        />
                        <Input
                            id="lastName"
                            placeholder="Apellido"
                            value={newPatientData.lastName}
                            autoComplete="off"
                            role="combobox"
                            aria-autocomplete="list"
                            onChange={(e) => {
                                // Only allow letters and spaces
                                const value = e.target.value.replace(/[^a-zA-ZáéíóúÁÉÍÓÚñÑüÜ\s]/g, '');
                                setNewPatientData((prev) => ({...prev, lastName: capitalizeName(value)}));
                            }}
                        />
                    </div>
                    <div className="space-y-[0.5rem]">
                        <Label>Cobertura</Label>
                        <div className="space-y-[0.5rem]">
                            <Select
                                open={isCoverageOpen}
                                onOpenChange={setIsCoverageOpen}
                                value={newPatientData.coverage}
                                onValueChange={(value) => setNewPatientData((prev) => ({
                                    ...prev,
                                    coverage: value,
                                    plan: ""
                                }))}
                                disabled={newPatientData.noCoverage}
                            >
                                <SelectTrigger id="coverage" className="z-[51]">
                                    <SelectValue placeholder="Seleccionar Cobertura"/>
                                </SelectTrigger>
                                <SelectContent className="z-[1001]">
                                    {DEFAULT_COVERAGES.filter(cov => cov.name !== "Sin Cobertura").map(cov => (
                                        <SelectItem key={cov.id} value={cov.name}>{cov.name}</SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            <Select
                                open={isPlanOpen}
                                onOpenChange={setIsPlanOpen}
                                value={newPatientData.plan}
                                onValueChange={(value) => setNewPatientData((prev) => ({...prev, plan: value}))}
                                disabled={!newPatientData.coverage || newPatientData.noCoverage}
                            >
                                <SelectTrigger id="plan" className="z-[51]">
                                    <SelectValue placeholder="Seleccionar Plan"/>
                                </SelectTrigger>
                                <SelectContent className="z-[51]">
                                    {newPatientData.coverage &&
                                        plansByCoverage[newPatientData.coverage]?.map((plan) => (
                                            <SelectItem key={plan} value={plan}>
                                                {plan}
                                            </SelectItem>
                                        ))}
                                </SelectContent>
                            </Select>
                            <div className="flex items-center space-x-[0.5rem]">
                                <Switch
                                    id="no-coverage"
                                    checked={newPatientData.noCoverage}
                                    onCheckedChange={(checked) =>
                                        setNewPatientData((prev) => ({
                                            ...prev,
                                            noCoverage: checked,
                                            coverage: checked ? "" : prev.coverage,
                                            plan: checked ? "" : prev.plan,
                                        }))
                                    }
                                />
                                <Label htmlFor="no-coverage">Sin Cobertura</Label>
                            </div>
                        </div>
                    </div>
                    <div className="space-y-[0.5rem]">
                        <Label>Información de Contacto</Label>
                        <div className="space-y-[0.5rem]">
                            <Input
                                id="dni"
                                placeholder="DNI"
                                value={newPatientData.identificationNumber}
                                autoComplete="off"
                                role="combobox"
                                aria-autocomplete="list"
                                onChange={(e) => {
                                    // Get current value and new value
                                    const currentValue = newPatientData.identificationNumber;
                                    const newValue = e.target.value;

                                    // Only allow numeric values
                                    const numericValue = newValue.replace(/[^0-9]/g, '');

                                    // Prevent DNI from starting with 0
                                    if (numericValue === "0" || (numericValue.length > 0 && numericValue[0] === "0")) {
                                        return;
                                    }

                                    // If already at max length (8) and trying to add more characters, don't update
                                    // This prevents removing the last digit when typing in the middle
                                    if (currentValue.length >= 8 && numericValue.length > 8) {
                                        return;
                                    }

                                    // Limit to 8 characters if needed
                                    const limitedValue = numericValue.length > 8 ? numericValue.slice(0, 8) : numericValue;

                                    setNewPatientData((prev) => ({...prev, identificationNumber: limitedValue}));

                                    // Clear error if DNI is valid length
                                    if (limitedValue.length >= 7 && limitedValue.length <= 8) {
                                        setPatientError("");
                                    } else if (limitedValue.length > 0) {
                                        setPatientError("El DNI debe tener entre 7 y 8 dígitos");
                                    }
                                }}
                            />
                            {patientError && (
                                <div className="text-xs text-red-600 mt-1">
                                    {patientError}
                                </div>
                            )}
                            <div className="custom-phone-input">
                                <PhoneInput
                                    defaultCountry="ar"
                                    value={newPatientData.phone}
                                    onChange={(phone) => {
                                        setNewPatientData((prev) => ({...prev, phone}));

                                        try {
                                            // Parse the phone number using Google's libphonenumber
                                            const phoneNumber = phoneUtil.parseAndKeepRawInput(phone);
                                            const isValid = phoneUtil.isValidNumber(phoneNumber);

                                            if (!isValid) {
                                                setPhoneError("El número de teléfono no es válido");
                                            } else {
                                                setPhoneError("");
                                            }
                                        } catch {
                                            setPhoneError("El número de teléfono no es válido");
                                        }
                                    }}
                                    inputStyle={{
                                        width: '100%',
                                        height: '2.5rem'
                                    }}
                                    className="w-full custom-phone-input with-dial-code-preview"
                                    placeholder="Teléfono"
                                    countrySelectorStyleProps={{
                                        buttonStyle: {
                                            paddingLeft: '10px',
                                            paddingRight: '5px'
                                        }
                                    }}
                                    hideDropdown={false}
                                    disableDialCodeAndPrefix={true}
                                    showDisabledDialCodeAndPrefix={true}
                                    disableFormatting={false}
                                    preferredCountries={['ar', 'cl', 'uy', 'br', 'py', 'bo', 'pe', 'ec', 'co', 've', 'mx', 'es']}
                                    countries={getSpanishCountries()}
                                />
                                {phoneError && (
                                    <div className="text-xs text-red-600 mt-1">
                                        {phoneError}
                                    </div>
                                )}
                            </div>
                            <Input
                                id="email"
                                placeholder="Email (opcional)"
                                value={newPatientData.email}
                                autoComplete="off"
                                role="combobox"
                                aria-autocomplete="list"
                                onChange={(e) => setNewPatientData((prev) => ({...prev, email: e.target.value}))}
                            />
                        </div>
                    </div>
                    <Input
                        id="dateOfBirth"
                        placeholder="Fecha de Nacimiento (DD/MM/AAAA)"
                        value={newPatientData.dateOfBirth}
                        autoComplete="off"
                        role="combobox"
                        aria-autocomplete="list"
                        onChange={(e) => {
                            let value = e.target.value.replace(/[^0-9]/g, '');

                            // Auto-format as DD/MM/YYYY
                            if (value.length >= 2) {
                                value = value.substring(0, 2) + '/' + value.substring(2);
                            }
                            if (value.length >= 5) {
                                value = value.substring(0, 5) + '/' + value.substring(5, 9);
                            }
                            if (value.length > 10) {
                                value = value.substring(0, 10);
                            }

                            setNewPatientData((prev) => ({...prev, dateOfBirth: value}));
                        }}
                        maxLength={10}
                    />

                    <div className="flex gap-[0.75rem] mt-[1.5rem]">
                        <Button onClick={() => setIsCreatingNewPatient(false)} variant="outline" className="flex-1">
                            Cancelar
                        </Button>
                        <Button
                            onClick={handleCreateNewPatient}
                            className="flex-1 bg-blue-500 hover:bg-blue-600"
                            disabled={!newPatientData.name || !newPatientData.lastName || !newPatientData.identificationNumber || !newPatientData.phone || newPatientData.phone === "+54" || !!phoneError || !newPatientData.dateOfBirth}
                        >
                            Crear
                        </Button>
                    </div>
                </div>
            )}

            {!isCreatingNewPatient && (
                <form onSubmit={handleSubmit}
                      className="flex flex-col gap-[1.5rem] border rounded-lg p-[1rem] bg-white border-black">
                    <div>
                        <h2 className="text-[1.125rem] font-semibold mb-[1rem]">
                            Agendar nuevo turno con {doctorInfo.fullName}
                        </h2>
                        <div className="text-[0.875rem] text-gray-600">{formatDate(selectedDate)}</div>
                    </div>

                    <div className="space-y-2">
                        <CollapsibleSection
                            title="Horario"
                            isOpen={openSections.schedule}
                            onOpenChange={() => toggleSection("schedule")}
                            summary={getScheduleSummary()}
                            isAnySectionOpen={isAnySectionOpen}
                            isComplete={!!formData.time}
                        >
                            <div className="space-y-[0.5rem]">
                                <Select
                                    value={formData.time}
                                    onValueChange={(value: string) => {
                                        setFormData((prev) => ({...prev, time: value}))
                                        if (value) {
                                            setTimeslotError("")

                                            // Determine which section to open next based on what's already filled
                                            if (formData.patientId) {
                                                // Open consultation type section since patient is already selected
                                                setOpenSections(prev => ({
                                                    ...prev,
                                                    schedule: false,
                                                    patient: false,
                                                    coverage: false,
                                                    consultationType: true
                                                }))
                                            } else {
                                                // No patient selected yet, open patient section
                                                setOpenSections(prev => ({
                                                    ...prev,
                                                    schedule: false,
                                                    patient: true,
                                                    coverage: false,
                                                    consultationType: false
                                                }))
                                            }
                                        }
                                    }}
                                    disabled={!!selectedTime}
                                >
                                    <SelectTrigger id="time" className="flex justify-between">
                                        <div className="text-left"><SelectValue placeholder="Seleccionar horario"/>
                                        </div>
                                    </SelectTrigger>
                                    <SelectContent className="z-[51]">
                                        {filteredTimeSlots.map((slot: string) => (
                                            <SelectItem key={slot} value={slot}>
                                                {slot} {!availableSlots.includes(slot) && "(Sobreturno)"}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                {timeslotError && <p className="text-red-500 text-[0.875rem]">{timeslotError}</p>}
                            </div>
                        </CollapsibleSection>

                        <CollapsibleSection
                            title="Paciente"
                            isOpen={openSections.patient}
                            onOpenChange={() => toggleSection("patient")}
                            summary={getPatientSummary()}
                            isAnySectionOpen={isAnySectionOpen}
                            isComplete={!!formData.patientId}
                        >
                            <div className="space-y-[0.5rem]" ref={patientSearchRef}>
                                <div className="relative">
                                    <Input
                                        id="patient"
                                        placeholder="Buscar paciente"
                                        value={searchTerm}
                                        autoComplete="off"
                                        name="search-patient"
                                        role="combobox"
                                        aria-autocomplete="list"
                                        onFocus={() => !formData.patientId && setDropdownOpen(true)}
                                        onChange={(e) => {
                                            if (!formData.patientId) {
                                                setSearchTerm(e.target.value)
                                                setDropdownOpen(true)
                                                setPatientNotFound(false)
                                                setSearchPerformed(false)
                                            }
                                        }}
                                        onKeyDown={(e) => {
                                            if (e.key === 'Enter' && !formData.patientId) {
                                                e.preventDefault()
                                                setSearchPerformed(true)
                                                const trimmedSearch = searchTerm.trim()
                                                if (trimmedSearch === '') {
                                                    setPatientNotFound(false)
                                                    return
                                                }
                                                const exactMatch = filteredPatients.length === 1 ? filteredPatients[0] : null
                                                if (exactMatch) {
                                                    setPatientNotFound(false)
                                                    setFormData((prev) => ({
                                                        ...prev,
                                                        patientId: exactMatch.id,
                                                    }))
                                                    setSearchTerm(exactMatch.name)
                                                    setDropdownOpen(false)

                                                    // Handle default coverage
                                                    if (exactMatch.healthInsurance) {
                                                        setHasDefaultCoverage(true)
                                                        setSelectedPatientHealthInsurance(exactMatch.healthInsurance.toPatientCreationRequestHealthInsuranceInformation())
                                                        setUseDefaultCoverage(true)
                                                        setOverrideNoCoverage(false)
                                                    } else {
                                                        setHasDefaultCoverage(true)
                                                        setSelectedPatientHealthInsurance(null)
                                                        setUseDefaultCoverage(true)
                                                        setOverrideHealthInsurance(null)
                                                        setOverrideNoCoverage(false)
                                                    }
                                                } else {
                                                    // Check if there are any matching patients in the filtered list
                                                    const hasMatches = filteredPatients.length > 0
                                                    setPatientNotFound(!hasMatches)
                                                }
                                            }
                                        }}
                                        ref={patientInputRef}
                                        className={formData.patientId ? 'bg-gray-100 text-gray-700' : ''}
                                        readOnly={!!formData.patientId}
                                    />
                                    {formData.patientId ? (
                                        <div
                                            className="absolute right-[0.75rem] top-1/2 transform -translate-y-1/2 flex items-center gap-2">
                                            <Info
                                                className="opacity-50 w-[1rem] h-[1rem] cursor-pointer hover:opacity-100 text-blue-500 hover:text-blue-700"
                                                onClick={() => {
                                                    if (formData.patientId) {
                                                        // Find the full patient object from the patients array
                                                        const fullPatient = patients.find(p => p.id === formData.patientId);
                                                        if (fullPatient) {
                                                            setSelectedPatientForDetails(fullPatient);
                                                            setShowPatientDetails(true);
                                                        }
                                                    }
                                                }}
                                            />
                                            <X
                                                className="opacity-50 w-[1rem] h-[1rem] cursor-pointer hover:opacity-100 text-gray-500 hover:text-red-500"
                                                onClick={clearPatientSelection}
                                            />
                                        </div>
                                    ) : (
                                        <Search
                                            className="absolute right-[0.75rem] top-1/2 transform -translate-y-1/2 opacity-50 w-[1rem] h-[1rem] cursor-pointer hover:opacity-100"
                                            onClick={() => {
                                                if (!formData.patientId) {
                                                    patientInputRef.current?.focus()
                                                    setDropdownOpen(true)

                                                    const trimmedSearch = searchTerm.trim()
                                                    if (trimmedSearch !== '') {
                                                        setSearchPerformed(true)
                                                        const exactMatch = filteredPatients.length === 1 ? filteredPatients[0] : null
                                                        if (exactMatch) {
                                                            setPatientNotFound(false)
                                                            setFormData((prev) => ({
                                                                ...prev,
                                                                patientId: exactMatch.id,
                                                            }))
                                                            setSearchTerm(exactMatch.name)
                                                            setDropdownOpen(false)

                                                            // Handle default coverage
                                                            if (exactMatch.healthInsurance) {
                                                                setHasDefaultCoverage(true)
                                                                setSelectedPatientHealthInsurance(exactMatch.healthInsurance.toPatientCreationRequestHealthInsuranceInformation())
                                                                setUseDefaultCoverage(true)
                                                                setOverrideNoCoverage(false)
                                                            } else {
                                                                setHasDefaultCoverage(true)
                                                                setSelectedPatientHealthInsurance(null)
                                                                setUseDefaultCoverage(true)
                                                                setOverrideHealthInsurance(null)
                                                                setOverrideNoCoverage(false)
                                                            }
                                                        } else {
                                                            // Check if there are any matching patients in the filtered list
                                                            const hasMatches = filteredPatients.length > 0
                                                            setPatientNotFound(!hasMatches)
                                                        }
                                                    }
                                                }
                                            }}
                                        />
                                    )}
                                    {dropdownOpen && (
                                        <div
                                            className="absolute z-10 w-full bg-white border border-gray-200 rounded-md mt-[0.25rem] shadow-lg">
                                            <div className="max-h-[15rem] overflow-auto">
                                                {patientNotFound && searchPerformed ? (
                                                    <div className="p-[0.5rem] text-[0.75rem] text-red-500">
                                                        No se encontró ningún paciente con los datos ingresados.
                                                    </div>
                                                ) : (
                                                    filteredPatients.map((patient: PatientResponse) => (
                                                        <div
                                                            key={patient.id}
                                                            className={`${dropdownItemStyle} ${formData.patientId === patient.id ? 'bg-blue-50' : ''} flex justify-between items-start`}
                                                        >
                                                            <div
                                                                className="flex-grow cursor-pointer"
                                                                onClick={() => {
                                                                    setFormData((prev) => ({
                                                                        ...prev,
                                                                        patientId: patient.id,
                                                                    }))
                                                                    setSearchTerm(patient.name)
                                                                    setDropdownOpen(false)

                                                                    // Handle default coverage
                                                                    setHasDefaultCoverage(true)
                                                                    setSelectedPatientHealthInsurance(patient.healthInsurance?.toPatientCreationRequestHealthInsuranceInformation())
                                                                    setUseDefaultCoverage(true)
                                                                    setOverrideHealthInsurance(null)
                                                                    setOverrideNoCoverage(false)
                                                                }}
                                                            >
                                                                <div className="font-medium">{patient.name}</div>
                                                                <div
                                                                    className="text-[0.75rem] text-gray-500">DNI: {patient.identificationNumber}</div>
                                                            </div>
                                                            <div
                                                                className="ml-2 p-1 cursor-pointer text-blue-500 hover:text-blue-700"
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    setSelectedPatientForDetails(patient);
                                                                    setShowPatientDetails(true);
                                                                }}
                                                            >
                                                                <Info size={16}/>
                                                            </div>
                                                        </div>
                                                    ))
                                                )}
                                            </div>
                                            <div
                                                className={`${dropdownButtonStyle} cursor-pointer`}
                                                onClick={() => setIsCreatingNewPatient(true)}
                                            >
                                                Crear nuevo paciente
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </CollapsibleSection>

                        {formData.patientId && hasDefaultCoverage && (
                            <CollapsibleSection
                                title="Cobertura"
                                isOpen={openSections.coverage}
                                onOpenChange={() => toggleSection("coverage")}
                                summary={getCoverageSummary()}
                                isAnySectionOpen={isAnySectionOpen}
                                hasWarning={isCoverageAccepted === false || (selectedTypes.length > 0 && selectedTypes.some(type =>
                                    isConsultationTypeExcluded(type)
                                ))}
                                isComplete={isCoverageValid()}
                            >
                                <div className="space-y-[0.5rem]">
                                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-200 mb-2">
                                        <div className="flex flex-col space-y-3">
                                            <div className="flex flex-col">
                                                <p className="text-sm text-blue-800">Cobertura registrada:</p>
                                                <p className="text-sm font-medium text-blue-700 mt-1">
                                                    {selectedPatientHealthInsurance ? ` ${selectedPatientHealthInsurance.name} ${selectedPatientHealthInsurance.plan}` : "Sin Cobertura"}
                                                </p>
                                            </div>
                                            <div
                                                className="flex items-center justify-between pt-2 border-t border-blue-200">
                                                <Label htmlFor="use-default-coverage"
                                                       className="text-sm font-medium text-blue-800">
                                                    {useDefaultCoverage ? "Usar esta cobertura" : "Usar otra cobertura"}
                                                </Label>
                                                <Switch
                                                    id="use-default-coverage"
                                                    checked={useDefaultCoverage}
                                                    onCheckedChange={(checked) => {
                                                        setUseDefaultCoverage(checked);
                                                        if (checked) {
                                                            setOverrideHealthInsurance(null);
                                                            setOverrideNoCoverage(false);
                                                        }
                                                    }}
                                                />
                                            </div>
                                        </div>
                                    </div>

                                    {!useDefaultCoverage && (
                                        <div className="space-y-[0.5rem] border-t border-gray-200 pt-3">
                                            <div className="space-y-[0.5rem]">
                                                <Select
                                                    value={overrideHealthInsurance?.name}
                                                    onValueChange={(value) => {
                                                        setOverrideHealthInsurance(
                                                            {
                                                                name: value,
                                                                plan: ""
                                                            }
                                                        )
                                                    }}
                                                    disabled={overrideNoCoverage}
                                                >
                                                    <SelectTrigger id="override-coverage"
                                                                   className="flex justify-between z-[51]">
                                                        <div className="text-left"><SelectValue
                                                            placeholder="Seleccionar Cobertura"/></div>
                                                    </SelectTrigger>
                                                    <SelectContent className="z-[1001]">
                                                        {DEFAULT_COVERAGES.filter(cov => cov.name !== "Sin Cobertura").map(cov => (
                                                            <SelectItem key={cov.id}
                                                                        value={cov.name}>{cov.name}</SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                                <Select
                                                    value={overrideHealthInsurance?.plan}
                                                    onValueChange={(value) => {
                                                        setOverrideHealthInsurance((prev) => ({
                                                            name: prev?.name || "",
                                                            plan: value
                                                        }))
                                                    }}
                                                    disabled={!overrideHealthInsurance || overrideNoCoverage}
                                                >
                                                    <SelectTrigger id="override-plan"
                                                                   className="flex justify-between z-[51]">
                                                        <div className="text-left"><SelectValue
                                                            placeholder="Seleccionar Plan"/></div>
                                                    </SelectTrigger>
                                                    <SelectContent className="z-[51]">
                                                        {overrideHealthInsurance?.name &&
                                                            plansByCoverage[overrideHealthInsurance.name]?.map((plan) => (
                                                                <SelectItem key={plan} value={plan}>
                                                                    {plan}
                                                                </SelectItem>
                                                            ))}
                                                    </SelectContent>
                                                </Select>
                                                {selectedPatientHealthInsurance && (
                                                    <div className="flex items-center space-x-[0.5rem]">
                                                        <Switch
                                                            id="override-no-coverage"
                                                            checked={overrideNoCoverage}
                                                            onCheckedChange={(checked) => {
                                                                setOverrideNoCoverage(checked);
                                                                if (checked) {
                                                                    setOverrideHealthInsurance(null);
                                                                }
                                                            }
                                                            }
                                                        />
                                                        <Label htmlFor="override-no-coverage">Sin Cobertura</Label>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </CollapsibleSection>
                        )}

                        <CollapsibleSection
                            title="Tipo de atención"
                            isOpen={openSections.consultationType}
                            onOpenChange={() => toggleSection("consultationType")}
                            summary={getConsultationTypeSummary()}
                            isAnySectionOpen={isAnySectionOpen}
                            isComplete={selectedTypes.length > 0 && consultationTypeConfirmed}
                        >
                            <div className="space-y-[0.5rem]" ref={consultationRef}>
                                <div className="relative">
                                    <div className="relative">
                                        <Input
                                            id="type"
                                            placeholder="Buscar tipo de atención"
                                            value={consultationSearch}
                                            autoComplete="off"
                                            name="search-consultation"
                                            role="combobox"
                                            aria-autocomplete="list"
                                            onFocus={() => setShowConsultationDropdown(true)}
                                            onChange={(e) => {
                                                setConsultationSearch(e.target.value)
                                                setShowConsultationDropdown(true)
                                            }}
                                            ref={consultationInputRef}
                                        />
                                        <Search
                                            className="absolute right-[0.75rem] top-1/2 transform -translate-y-1/2 opacity-50 w-[1rem] h-[1rem] cursor-pointer"
                                            onClick={() => {
                                                consultationInputRef.current?.focus()
                                                setShowConsultationDropdown(true)
                                            }}/>
                                    </div>

                                    {showConsultationDropdown && (
                                        <div
                                            className="absolute z-20 w-full bg-white border border-gray-200 rounded-md mt-[0.25rem] shadow-lg">
                                            <div className="max-h-[15rem] overflow-auto">
                                                {doctorConsultationTypes.length === 0 ? (
                                                    <div className="p-2 text-sm text-gray-500">No hay tipos de atención
                                                        configurados</div>
                                                ) : (
                                                    filteredConsultations.map((type) => (
                                                        <div
                                                            key={type.name}
                                                            className={cn(
                                                                dropdownItemStyle,
                                                                'text-white bg-blue-500 hover:bg-blue-400'
                                                            )}
                                                            onClick={() => handleConsultationSelect(type)}
                                                        >
                                                            {type.name}
                                                            {`(${type.appointmentIntervalAmount * appointmentDuration} min)`}
                                                            {/* Show price when coverage is "Sin Cobertura" or when coverage is not accepted */}
                                                            {(isSinCobertura() || isConsultationTypeExcluded(type)) &&
                                                                ` - $${type.getPriceForHealthInsurance(coverageToCheck ? {...coverageToCheck} : undefined,
                                                                    doctorInfo.isHealthInsuranceAccepted(coverageToCheck ? {...coverageToCheck} : null)).toLocaleString('es-AR', {minimumFractionDigits: 0}).replace(/,/g, '.')}`}
                                                        </div>
                                                    ))
                                                )}
                                            </div>
                                            <div className="border-t p-2 flex justify-between items-center">
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    className="text-gray-600 hover:bg-gray-100 h-9"
                                                    onClick={() => {
                                                        setShowAllConsultationsModal(true)
                                                        setShowConsultationDropdown(false)
                                                    }}
                                                >
                                                    Ver todas
                                                </Button>
                                                <Button
                                                    size="sm"
                                                    className="bg-blue-500 hover:bg-blue-600 text-white h-9"
                                                    onClick={() => {
                                                        // Mark consultation type as confirmed when clicking Confirmar button
                                                        if (selectedTypes.length > 0) {
                                                            setConsultationTypeConfirmed(true)
                                                            setShowConsultationDropdown(false)
                                                            // Show information popup after confirming selection
                                                            showSelectedConsultationInfo()
                                                        } else {
                                                            setShowConsultationDropdown(false)
                                                        }
                                                    }}
                                                >
                                                    Confirmar
                                                </Button>
                                            </div>
                                        </div>
                                    )}
                                </div>
                                <div className="flex flex-wrap gap-[0.5rem] mt-[0.5rem]">
                                    {selectedTypes.map((type) => (
                                        <span key={type.consultationTypeId}
                                              className="px-[0.5rem] py-[0.25rem] bg-blue-100 text-blue-800 rounded-full text-[0.75rem] hover:bg-blue-200 flex items-center">
                                            {type.name}
                                            {(() => {
                                                return consultationTypeHasInfo(type) ? (
                                                    <button
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            e.preventDefault();
                                                            // This button still shows info immediately when clicked
                                                            showConsultationInfo([type]);
                                                        }}
                                                        className="ml-[0.25rem] rounded-full hover:bg-blue-300 px-[0.25rem]"
                                                        aria-label={`Ver información de ${type}`}
                                                        type="button" // Explicitly set type to button to prevent form submission
                                                    >
                                                        <AlertCircle className="h-3 w-3 text-blue-600"/>
                                                    </button>
                                                ) : null;
                                            })()}
                                            <button
                                                onClick={() => {
                                                    setSelectedTypes((prev) => prev.filter((t) => t !== type))
                                                    // If removing a type, check if there are still types selected
                                                    // If no types left, set confirmed to false
                                                    setConsultationTypeConfirmed(prev => prev && selectedTypes.length > 1)
                                                }}
                                                className="ml-[0.25rem] rounded-full hover:bg-blue-200 px-[0.375rem]"
                                                aria-label={`Remover ${type}`}
                                            >
                        ×
                      </button>
                    </span>
                                    ))}
                                </div>
                            </div>
                        </CollapsibleSection>
                    </div>

                    {/* Warning message for unbookable consultation types */}
                    {hasUnbookableConsultationType() && (
                        <div className="p-3 bg-red-50 border border-red-200 rounded-lg mb-3">
                            <p className="text-red-700 flex items-start gap-2">
                                <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5"/>
                                <span className="text-sm text-justify">
                  No se puede reservar este turno porque uno o más tipos de atención seleccionados no aceptan la cobertura actual ni pagos particulares.
                </span>
                            </p>
                        </div>
                    )}

                    <div className="flex gap-[0.75rem] mt-[0.2rem]">
                        <Button onClick={onCancel} variant="outline" className="flex-1">
                            Cancelar
                        </Button>
                        <Button
                            type="submit"
                            className="flex-1 bg-blue-500 hover:bg-blue-600 text-white"
                            disabled={
                                !formData.patientId ||
                                !formData.time ||
                                selectedTypes.length === 0 ||
                                !isCoverageValid() ||
                                !consultationTypeConfirmed ||
                                (isSinCobertura() && selectedTypes.some(type =>
                                    !type.acceptsSelfPaidPatient
                                )) ||
                                hasUnbookableConsultationType()
                            }
                        >
                            Confirmar
                        </Button>
                    </div>

                    {showAllConsultationsModal && (
                        <div className="fixed inset-0 bg-black/50 z-[50] flex items-center justify-center"
                             onClick={() => setShowAllConsultationsModal(false)}>
                            <div
                                className="w-full max-w-[37.5rem] bg-white rounded-xl shadow-2xl overflow-hidden max-h-[80vh] mx-[1rem]"
                                onClick={(e) => e.stopPropagation()}
                            >
                                <div className="p-[1.5rem] border-b bg-gray-50">
                                    <h3 className="text-[1.25rem] font-semibold text-gray-800">Seleccionar tipos de
                                        atención</h3>
                                </div>

                                <div className="max-h-[60vh] overflow-auto p-[1rem] grid gap-[0.25rem]">
                                    {doctorConsultationTypes.map((type) => (
                                        <div
                                            key={type.name}
                                            className={`p-[0.75rem] text-[0.875rem] rounded-lg cursor-pointer transition-colors flex items-center justify-between ${
                                                selectedTypes.some(selectedType => selectedType.consultationTypeId === type.consultationTypeId) ? 'bg-blue-500 text-white hover:bg-blue-400' : 'hover:bg-gray-100'
                                            }`}
                                        >
                                            <div onClick={() => handleConsultationSelect(type)}>
                                                {type.name}
                                                {`(${type.appointmentIntervalAmount * appointmentDuration} min)`}
                                                {/* Show price in the "Ver todas" modal when coverage is "Sin Cobertura" or not accepted */}
                                                {(isSinCobertura() || isConsultationTypeExcluded(type)) &&
                                                    ` - $${type.getPriceForHealthInsurance(coverageToCheck ? {...coverageToCheck} : undefined,
                                                        doctorInfo.isHealthInsuranceAccepted(coverageToCheck ? {...coverageToCheck} : null)).toLocaleString('es-AR', {minimumFractionDigits: 0}).replace(/,/g, '.')}`}
                                            </div>
                                            {(() => {

                                                return consultationTypeHasInfo(type) ? (
                                                    <button
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            e.preventDefault();
                                                            // This button still shows info immediately when clicked
                                                            showConsultationInfo([type]);
                                                        }}
                                                        className={`ml-2 rounded-full p-1 ${
                                                            selectedTypes.some(selectedType => selectedType.consultationTypeId === type.consultationTypeId) ? 'hover:bg-blue-600 text-white' : 'hover:bg-gray-200 text-blue-600'
                                                        }`}
                                                        aria-label={`Ver información de ${type.name}`}
                                                        type="button" // Explicitly set type to button to prevent form submission
                                                    >
                                                        <AlertCircle className="h-4 w-4"/>
                                                    </button>
                                                ) : null;
                                            })()}
                                        </div>
                                    ))}
                                </div>

                                <div className="flex gap-[1rem] p-[1rem] border-t bg-gray-50">
                                    <Button
                                        variant="outline"
                                        className="flex-1"
                                        onClick={() => setShowAllConsultationsModal(false)}
                                    >
                                        Cancelar
                                    </Button>
                                    <Button
                                        className="flex-1 bg-blue-500 hover:bg-blue-600 text-white"
                                        onClick={() => {
                                            // Mark consultation type as confirmed when confirming selection in modal
                                            if (selectedTypes.length > 0) {
                                                setConsultationTypeConfirmed(true)
                                                setShowAllConsultationsModal(false)
                                                // Show information popup after confirming selection
                                                showSelectedConsultationInfo()
                                            } else {
                                                setShowAllConsultationsModal(false)
                                            }
                                        }}
                                    >
                                        Confirmar selección
                                    </Button>
                                </div>
                            </div>
                        </div>
                    )}
                </form>
            )}

            {/* Patient Details Dialog */}
            <PatientDetailsDialog
                patient={selectedPatientForDetails}
                isOpen={showPatientDetails}
                onClose={() => setShowPatientDetails(false)}
            />

            {/* Instructions Dialog */}
            <ConsultationInfoTable
                isOpen={showInstructionsDialog}
                onOpenChange={setShowInstructionsDialog}
                consultationTypesInfo={consultationTypesInfo}
                selectedCoverage={coverageToCheck ? `${coverageToCheck.name} ${coverageToCheck.plan}` : "Sin Cobertura"}
            />
        </>
    )
}

